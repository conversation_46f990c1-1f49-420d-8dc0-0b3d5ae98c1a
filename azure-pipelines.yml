trigger:
- main

pool:
  vmImage: 'windows-latest'

variables:
  solution: '**/*.sln'
  buildPlatform: 'x64'
  buildConfiguration: 'Release'

name: pfavizkhang_$(Date:yyyyMMdd)$(Rev:.r)

steps:
- powershell: .\install-dxsdk.ps1
  displayName: 'Install DirectX SDK (June 2010)'

- task: VSBuild@1
  displayName: 'Build (x64/Release/Clang)'
  inputs:
    solution: '$(solution)'
    platform: '$(buildPlatform)'
    configuration: '$(buildConfiguration)'

- task: CopyFiles@2
  inputs:
    sourceFolder: 'Release'
    contents: '*.exe'
    targetFolder: $(Build.ArtifactStagingDirectory)

- task: GithubRelease@0
  displayName: 'Create GitHub Release'
  inputs:
    gitHubConnection: github.com_khang06
    repositoryName: khang06/PianoFromAbove
    tagSource: manual
    tag: $(Build.BuildNumber)
    assets: $(Build.ArtifactStagingDirectory)/*.exe