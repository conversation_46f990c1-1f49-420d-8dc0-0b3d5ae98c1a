﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseSSE|Win32">
      <Configuration>ReleaseSSE</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseSSE|x64">
      <Configuration>ReleaseSSE</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{DD4C822F-844F-40BF-8113-129B04EDE199}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>PianoFromAbove</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(DXSDK_DIR)Include;C:\Users\<USER>\Dev\boost_1_55_0;C:\Users\<USER>\Dev\protobuf-2.5.0\vsprojects\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(DXSDK_DIR)Lib\x86;C:\Users\<USER>\Dev\protobuf-2.5.0\vsprojects\Release;$(LibraryPath)</LibraryPath>
    <TargetName>PFA-1.1.0viz-x86</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>$(ProjectDir);$(IncludePath)</IncludePath>
    <LibraryPath>$(ProjectDir)lzma\lib;$(ProjectDir)freetype\lib;$(LibraryPath)</LibraryPath>
    <OutDir>$(SolutionDir)\$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\$(Platform)\</IntDir>
    <TargetName>PFA-1.1.0viz-x86_64</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(DXSDK_DIR)Include;C:\Users\<USER>\Dev\protobuf-2.5.0\vsprojects\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(DXSDK_DIR)Lib\x86;C:\Users\<USER>\Dev\protobuf-2.5.0\vsprojects\Release;$(LibraryPath)</LibraryPath>
    <TargetName>PFA-1.1.0viz-x86</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(DXSDK_DIR)Include;C:\Users\<USER>\Dev\protobuf-2.5.0\vsprojects\include;$(IncludePath)</IncludePath>
    <LibraryPath>$(DXSDK_DIR)Lib\x86;C:\Users\<USER>\Dev\protobuf-2.5.0\vsprojects\Release;$(LibraryPath)</LibraryPath>
    <TargetName>PFA-1.1.0viz-x86</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(ProjectDir);$(IncludePath)</IncludePath>
    <LibraryPath>$(ProjectDir)lzma\lib;$(ProjectDir)freetype\lib;$(LibraryPath)</LibraryPath>
    <OutDir>$(SolutionDir)\$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\$(Platform)\</IntDir>
    <TargetName>PFA-1.1.0viz-AVX2</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(ProjectDir);$(IncludePath)</IncludePath>
    <LibraryPath>$(ProjectDir)lzma\lib;$(ProjectDir)freetype\lib;$(LibraryPath)</LibraryPath>
    <OutDir>$(SolutionDir)\$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\$(Platform)\</IntDir>
    <TargetName>PFA-1.1.0viz-SSE42</TargetName>
  </PropertyGroup>
  <PropertyGroup Label="Vcpkg">
    <VcpkgEnabled>false</VcpkgEnabled>
    <VcpkgAutoLink>false</VcpkgAutoLink>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;WINVER=0x0501;_WIN32_WINNT=0x0501;TIXML_USE_STL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalManifestDependencies>"type='Win32' name='Microsoft.Windows.Common-Controls' version='*******' processorArchitecture='X86' publicKeyToken='6595b64144ccf1df' language='*'";%(AdditionalManifestDependencies)</AdditionalManifestDependencies>
      <AdditionalDependencies>d3d9.lib;d3dx9d.lib;WinMM.lib;Comctl32.lib;UxTheme.lib;Advapi32.lib;Msimg32.lib;libprotobuf-lite.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <Version>1.1.0viz</Version>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;TIXML_USE_STL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <EnableEnhancedInstructionSet>AdvancedVectorExtensions2</EnableEnhancedInstructionSet>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalManifestDependencies>"type='Win32' name='Microsoft.Windows.Common-Controls' version='*******' processorArchitecture='*' publicKeyToken='6595b64144ccf1df' language='*'";%(AdditionalManifestDependencies)</AdditionalManifestDependencies>
      <AdditionalDependencies>lzma.lib;winhttp.lib;freetype-x64.lib;libpng16-x64.lib;zlib-x64.lib;brotlicommon-x64.lib;brotlidec-x64.lib;bz2-x64.lib;dxguid.lib;dxgi.lib;d3d12.lib;Psapi.lib;Shlwapi.lib;Dwmapi.lib;Comctl32.lib;WinMM.lib;UxTheme.lib;Advapi32.lib;Msimg32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <Version>
      </Version>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;WINVER=0x0501;_WIN32_WINNT=0x0501;TIXML_USE_STL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalManifestDependencies>"type='Win32' name='Microsoft.Windows.Common-Controls' version='*******' processorArchitecture='X86' publicKeyToken='6595b64144ccf1df' language='*'";%(AdditionalManifestDependencies)</AdditionalManifestDependencies>
      <AdditionalDependencies>d3d9.lib;d3dx9.lib;WinMM.lib;Comctl32.lib;UxTheme.lib;Advapi32.lib;Msimg32.lib;libprotobuf-lite.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <Version>1.1.0viz</Version>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;WINVER=0x0501;_WIN32_WINNT=0x0501;TIXML_USE_STL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalManifestDependencies>"type='Win32' name='Microsoft.Windows.Common-Controls' version='*******' processorArchitecture='X86' publicKeyToken='6595b64144ccf1df' language='*'";%(AdditionalManifestDependencies)</AdditionalManifestDependencies>
      <AdditionalDependencies>d3d9.lib;d3dx9.lib;WinMM.lib;Comctl32.lib;UxTheme.lib;Advapi32.lib;Msimg32.lib;libprotobuf-lite.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <Version>1.1.0viz</Version>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;TIXML_USE_STL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <AdditionalOptions>/clang:-Ofast %(AdditionalOptions)</AdditionalOptions>
      <FloatingPointModel>Fast</FloatingPointModel>
      <EnableEnhancedInstructionSet>AdvancedVectorExtensions2</EnableEnhancedInstructionSet>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalManifestDependencies>"type='Win32' name='Microsoft.Windows.Common-Controls' version='*******' processorArchitecture='*' publicKeyToken='6595b64144ccf1df' language='*'";%(AdditionalManifestDependencies)</AdditionalManifestDependencies>
      <AdditionalDependencies>lzma.lib;winhttp.lib;freetype-x64.lib;libpng16-x64.lib;zlib-x64.lib;brotlicommon-x64.lib;brotlidec-x64.lib;bz2-x64.lib;dxguid.lib;dxgi.lib;d3d12.lib;Psapi.lib;Shlwapi.lib;Dwmapi.lib;Comctl32.lib;WinMM.lib;UxTheme.lib;Advapi32.lib;Msimg32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <Version>
      </Version>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;TIXML_USE_STL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <AdditionalOptions>/clang:-Ofast /clang:-msse4.2 %(AdditionalOptions)</AdditionalOptions>
      <FloatingPointModel>Fast</FloatingPointModel>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalManifestDependencies>"type='Win32' name='Microsoft.Windows.Common-Controls' version='*******' processorArchitecture='*' publicKeyToken='6595b64144ccf1df' language='*'";%(AdditionalManifestDependencies)</AdditionalManifestDependencies>
      <AdditionalDependencies>lzma.lib;winhttp.lib;freetype-x64.lib;libpng16-x64.lib;zlib-x64.lib;brotlicommon-x64.lib;brotlidec-x64.lib;bz2-x64.lib;dxguid.lib;dxgi.lib;d3d12.lib;Psapi.lib;Shlwapi.lib;Dwmapi.lib;Comctl32.lib;WinMM.lib;UxTheme.lib;Advapi32.lib;Msimg32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <Version>
      </Version>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="Config.h" />
    <ClInclude Include="ConfigProcs.h" />
    <ClInclude Include="d3dx12\d3dx12.h" />
    <ClInclude Include="d3dx12\d3dx12_barriers.h" />
    <ClInclude Include="d3dx12\d3dx12_check_feature_support.h" />
    <ClInclude Include="d3dx12\d3dx12_core.h" />
    <ClInclude Include="d3dx12\d3dx12_default.h" />
    <ClInclude Include="d3dx12\d3dx12_pipeline_state_stream.h" />
    <ClInclude Include="d3dx12\d3dx12_render_pass.h" />
    <ClInclude Include="d3dx12\d3dx12_resource_helpers.h" />
    <ClInclude Include="d3dx12\d3dx12_root_signature.h" />
    <ClInclude Include="d3dx12\d3dx12_state_object.h" />
    <ClInclude Include="freetype\freetype.h" />
    <ClInclude Include="freetype\ftadvanc.h" />
    <ClInclude Include="freetype\ftbbox.h" />
    <ClInclude Include="freetype\ftbdf.h" />
    <ClInclude Include="freetype\ftbitmap.h" />
    <ClInclude Include="freetype\ftbzip2.h" />
    <ClInclude Include="freetype\ftcache.h" />
    <ClInclude Include="freetype\ftchapters.h" />
    <ClInclude Include="freetype\ftcid.h" />
    <ClInclude Include="freetype\ftcolor.h" />
    <ClInclude Include="freetype\ftdriver.h" />
    <ClInclude Include="freetype\fterrdef.h" />
    <ClInclude Include="freetype\fterrors.h" />
    <ClInclude Include="freetype\ftfntfmt.h" />
    <ClInclude Include="freetype\ftgasp.h" />
    <ClInclude Include="freetype\ftglyph.h" />
    <ClInclude Include="freetype\ftgxval.h" />
    <ClInclude Include="freetype\ftgzip.h" />
    <ClInclude Include="freetype\ftimage.h" />
    <ClInclude Include="freetype\ftincrem.h" />
    <ClInclude Include="freetype\ftlcdfil.h" />
    <ClInclude Include="freetype\ftlist.h" />
    <ClInclude Include="freetype\ftlogging.h" />
    <ClInclude Include="freetype\ftlzw.h" />
    <ClInclude Include="freetype\ftmac.h" />
    <ClInclude Include="freetype\ftmm.h" />
    <ClInclude Include="freetype\ftmodapi.h" />
    <ClInclude Include="freetype\ftmoderr.h" />
    <ClInclude Include="freetype\ftotval.h" />
    <ClInclude Include="freetype\ftoutln.h" />
    <ClInclude Include="freetype\ftparams.h" />
    <ClInclude Include="freetype\ftpfr.h" />
    <ClInclude Include="freetype\ftrender.h" />
    <ClInclude Include="freetype\ftsizes.h" />
    <ClInclude Include="freetype\ftsnames.h" />
    <ClInclude Include="freetype\ftstroke.h" />
    <ClInclude Include="freetype\ftsynth.h" />
    <ClInclude Include="freetype\ftsystem.h" />
    <ClInclude Include="freetype\fttrigon.h" />
    <ClInclude Include="freetype\fttypes.h" />
    <ClInclude Include="freetype\ftwinfnt.h" />
    <ClInclude Include="freetype\otsvg.h" />
    <ClInclude Include="freetype\t1tables.h" />
    <ClInclude Include="freetype\ttnameid.h" />
    <ClInclude Include="freetype\tttables.h" />
    <ClInclude Include="freetype\tttags.h" />
    <ClInclude Include="ft2build.h" />
    <ClInclude Include="GameState.h" />
    <ClInclude Include="Globals.h" />
    <ClInclude Include="imgui\imconfig.h" />
    <ClInclude Include="imgui\imgui.h" />
    <ClInclude Include="imgui\imgui_freetype.h" />
    <ClInclude Include="imgui\imgui_impl_dx12.h" />
    <ClInclude Include="imgui\imgui_impl_win32.h" />
    <ClInclude Include="imgui\imgui_internal.h" />
    <ClInclude Include="imgui\imstb_rectpack.h" />
    <ClInclude Include="imgui\imstb_textedit.h" />
    <ClInclude Include="imgui\imstb_truetype.h" />
    <ClInclude Include="lzma.h" />
    <ClInclude Include="lzma\base.h" />
    <ClInclude Include="lzma\bcj.h" />
    <ClInclude Include="lzma\block.h" />
    <ClInclude Include="lzma\check.h" />
    <ClInclude Include="lzma\container.h" />
    <ClInclude Include="lzma\delta.h" />
    <ClInclude Include="lzma\filter.h" />
    <ClInclude Include="lzma\hardware.h" />
    <ClInclude Include="lzma\index.h" />
    <ClInclude Include="lzma\index_hash.h" />
    <ClInclude Include="lzma\lzma12.h" />
    <ClInclude Include="lzma\stream_flags.h" />
    <ClInclude Include="lzma\version.h" />
    <ClInclude Include="lzma\vli.h" />
    <ClInclude Include="MainProcs.h" />
    <ClInclude Include="MIDI.h" />
    <ClInclude Include="Misc.h" />
    <ClInclude Include="Renderer.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="tinyxml\tinystr.h" />
    <ClInclude Include="tinyxml\tinyxml.h" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="PianoFromAbove.rc" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Config.cpp" />
    <ClCompile Include="ConfigProcs.cpp" />
    <ClCompile Include="GameState.cpp" />
    <ClCompile Include="imgui\imgui.cpp" />
    <ClCompile Include="imgui\imgui_draw.cpp" />
    <ClCompile Include="imgui\imgui_freetype.cpp" />
    <ClCompile Include="imgui\imgui_impl_dx12.cpp" />
    <ClCompile Include="imgui\imgui_impl_win32.cpp" />
    <ClCompile Include="imgui\imgui_tables.cpp" />
    <ClCompile Include="imgui\imgui_widgets.cpp" />
    <ClCompile Include="MainProcs.cpp" />
    <ClCompile Include="MIDI.cpp">
      <RuntimeLibrary Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">MultiThreadedDebug</RuntimeLibrary>
      <RuntimeLibrary Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MultiThreadedDebug</RuntimeLibrary>
    </ClCompile>
    <ClCompile Include="Misc.cpp" />
    <ClCompile Include="PianoFromAbove.cpp" />
    <ClCompile Include="Renderer.cpp" />
    <ClCompile Include="tinyxml\tinystr.cpp" />
    <ClCompile Include="tinyxml\tinyxml.cpp" />
    <ClCompile Include="tinyxml\tinyxmlerror.cpp" />
    <ClCompile Include="tinyxml\tinyxmlparser.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Images\Godowsky - Study of Chopin Etude Op. 10, No. 1.mid" />
    <None Include="Images\Lock.bmp" />
    <None Include="Images\Mirror Small.bmp" />
    <None Include="Images\PFA Icon.ico" />
    <None Include="Images\mediaiconssmall.bmp" />
    <None Include="images\Welcome.ico" />
    <None Include="common.hlsli" />
  </ItemGroup>
  <ItemGroup>
    <FxCompile Include="background_ps.hlsl">
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|Win32'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">Pixel</ShaderType>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">5.1</ShaderModel>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">5.1</ShaderModel>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">5.1</ShaderModel>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">g_pBackgroundPixelShader</VariableName>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ProjectDir)BackgroundPixelShader.h</HeaderFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
      </ObjectFileOutput>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">g_pBackgroundPixelShader</VariableName>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">g_pBackgroundPixelShader</VariableName>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ProjectDir)BackgroundPixelShader.h</HeaderFileOutput>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">$(ProjectDir)BackgroundPixelShader.h</HeaderFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
      </ObjectFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">
      </ObjectFileOutput>
    </FxCompile>
    <FxCompile Include="background_vs.hlsl">
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|Win32'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">Vertex</ShaderType>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">5.1</ShaderModel>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">5.1</ShaderModel>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">5.1</ShaderModel>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ProjectDir)BackgroundVertexShader.h</HeaderFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
      </ObjectFileOutput>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ProjectDir)BackgroundVertexShader.h</HeaderFileOutput>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">$(ProjectDir)BackgroundVertexShader.h</HeaderFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
      </ObjectFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">
      </ObjectFileOutput>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">g_pBackgroundVertexShader</VariableName>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">g_pBackgroundVertexShader</VariableName>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">g_pBackgroundVertexShader</VariableName>
    </FxCompile>
    <FxCompile Include="note_ps.hlsl">
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|Win32'">Pixel</ShaderType>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ProjectDir)NotePixelShader.h</HeaderFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
      </ObjectFileOutput>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ProjectDir)NotePixelShader.h</HeaderFileOutput>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">$(ProjectDir)NotePixelShader.h</HeaderFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
      </ObjectFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">
      </ObjectFileOutput>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">g_pNotePixelShader</VariableName>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">g_pNotePixelShader</VariableName>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">g_pNotePixelShader</VariableName>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">5.1</ShaderModel>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">5.1</ShaderModel>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">5.1</ShaderModel>
    </FxCompile>
    <FxCompile Include="note_vs.hlsl">
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|Win32'">Vertex</ShaderType>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">g_pNoteVertexShader</VariableName>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">g_pNoteVertexShader</VariableName>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
      </ObjectFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">
      </ObjectFileOutput>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">g_pNoteVertexShader</VariableName>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ProjectDir)NoteVertexShader.h</HeaderFileOutput>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ProjectDir)NoteVertexShader.h</HeaderFileOutput>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">$(ProjectDir)NoteVertexShader.h</HeaderFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
      </ObjectFileOutput>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">5.1</ShaderModel>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">5.1</ShaderModel>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">5.1</ShaderModel>
    </FxCompile>
    <FxCompile Include="rect_ps.hlsl">
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|Win32'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Pixel</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">Pixel</ShaderType>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">g_pRectPixelShader</VariableName>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ProjectDir)RectPixelShader.h</HeaderFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
      </ObjectFileOutput>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">g_pRectPixelShader</VariableName>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">g_pRectPixelShader</VariableName>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ProjectDir)RectPixelShader.h</HeaderFileOutput>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">$(ProjectDir)RectPixelShader.h</HeaderFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
      </ObjectFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">
      </ObjectFileOutput>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">5.1</ShaderModel>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">5.1</ShaderModel>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">5.1</ShaderModel>
    </FxCompile>
    <FxCompile Include="rect_vs.hlsl">
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|Win32'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Vertex</ShaderType>
      <ShaderType Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">Vertex</ShaderType>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">g_pRectVertexShader</VariableName>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(ProjectDir)RectVertexShader.h</HeaderFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
      </ObjectFileOutput>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">g_pRectVertexShader</VariableName>
      <VariableName Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">g_pRectVertexShader</VariableName>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(ProjectDir)RectVertexShader.h</HeaderFileOutput>
      <HeaderFileOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">$(ProjectDir)RectVertexShader.h</HeaderFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
      </ObjectFileOutput>
      <ObjectFileOutput Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">
      </ObjectFileOutput>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">5.1</ShaderModel>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">5.1</ShaderModel>
      <ShaderModel Condition="'$(Configuration)|$(Platform)'=='ReleaseSSE|x64'">5.1</ShaderModel>
    </FxCompile>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>