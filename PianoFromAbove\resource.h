//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by PianoFromAbove.rc
//
#define IDM_MAINMENU                    101
#define IDD_LIBDLG                      101
#define IDA_MAINMENU                    103
#define IDR_CONTEXTMENU                 106
#define IDD_PP1_VISUAL                  107
#define IDD_PP2_AUDIO                   108
#define IDD_PP3_VIDEO                   109
#define IDD_PP5_LIBRARY                 110
#define IDD_PP5_VIZ                     110
#define IDB_MEDIAICONSSMALL             125
#define IDR_HIDDENCMDS                  126
#define IDD_TRACKSETTINGS               127
#define IDD_PP4_CONTROLS                128
#define IDI_PFAICON                     134
#define IDB_SPLASH                      135
#define IDB_WELCOME                     136
#define IDI_ICON1                       137
#define IDI_WELCOME                     137
#define IDD_ABOUT                       138
#define IDB_LOCK                        146
#define IDR_SPLASHMIDI                  149
#define IDD_LOADING                     153
#define IDD_SETRESOLUTION               158
#define IDC_TRACKS                      1001
#define IDC_HIDEKEYBOARD                1004
#define IDC_FLIPSCREEN                  1005
#define IDC_COLOR1                      1006
#define IDC_COLOR2                      1007
#define IDC_COLOR3                      1008
#define IDC_COLOR4                      1009
#define IDC_COLOR5                      1010
#define IDC_COLOR6                      1011
#define IDC_BKGCOLOR                    1012
#define IDC_COLOR7                      1013
#define IDC_BARCOLOR                    1013
#define IDC_SHOWALLKEYS                 1020
#define IDC_SHOWSONGKEYS                1021
#define IDC_SHOWCUSTOMKEYS              1022
#define IDC_LRARROWS                    1023
#define IDC_LRARROWSSPIN                1024
#define IDC_UDARROWS                    1025
#define IDC_UDARROWSSPIN                1026
#define IDC_MIDIIN                      1028
#define IDC_WINDOWSECS                  1028
#define IDC_VQCAPACITY                  1028
#define IDC_MIDIOUT                     1029
#define IDC_LRARROWSSPIN2               1029
#define IDC_WINDOWSECSSPIN              1029
#define IDC_DIRECT3D                    1031
#define IDC_OPENGL                      1032
#define IDC_GDI                         1033
#define IDC_DISPLAYFPS                  1034
#define IDC_LIMITFPS                    1035
#define IDC_ADDFILE                     1044
#define IDC_ADDFOLDER                   1045
#define IDC_REMOVE                      1046
#define IDC_ALWAYSADD                   1047
#define IDC_ASSOCIATEFILES              1047
#define IDC_RESTOREDEFAULTS             1048
#define IDC_LIBRARY                     1049
#define IDC_LIBRARYFILES                1050
#define IDC_VOLUME                      1053
#define IDC_SPEED                       1054
#define IDC_HIDDENOK                    1056
#define IDC_FILE                        1057
#define IDC_FOLDER                      1058
#define IDC_NOTES                       1059
#define IDC_LENGTH                      1060
#define IDC_SHOWCONTROLS                1061
#define IDC_FIRSTKEY                    1062
#define IDC_LASTKEY                     1063
#define IDC_THROUGH                     1064
#define IDC_COMBO1                      1072
#define IDC_MARKERENC                   1072
#define IDC_COMBO2                      1073
#define IDC_RESET                       1076
#define IDC_PIANOGROUP                  1078
#define IDC_EDIT1                       1079
#define IDC_SPLASHMIDI                  1079
#define IDC_WIDTH                       1079
#define IDC_BACKGROUND                  1080
#define IDC_WIDTH2                      1080
#define IDC_HEIGHT                      1080
#define IDC_BUTTON7                     1081
#define IDC_FONT                        1081
#define IDC_RADIO1                      1086
#define IDC_RADIO2                      1087
#define IDC_RADIO3                      1088
#define IDC_PICTURE                     1089
#define IDC_EDIT2                       1090
#define IDC_VQSIZE                      1090
#define IDC_UISCALE                     1090
#define IDC_LICENSE                     1093
#define IDC_LOCK                        1094
#define IDC_NSPEED                      1095
#define IDC_CHECK1                      1096
#define IDC_LOADINGPROGRESS             1097
#define IDC_CHECK2                      1097
#define IDC_LOADINGDESC                 1098
#define IDC_LOADINGNUM                  1099
#define IDC_STATS                       1102
#define IDC_MARKERS                     1103
#define IDC_SPLASHBROWSE                1104
#define IDC_BUTTON2                     1105
#define IDC_SPLASHRESET                 1105
#define IDC_MEMUSAGE                    1106
#define IDC_PITCHBENDS                  1106
#define IDC_TICKBASED                   1107
#define IDC_PITCHBENDS2                 1108
#define IDC_FFMPEG                      1108
#define IDC_BACKGROUNDBROWSE            1109
#define IDC_KDMAPI                      1109
#define IDC_SPLASHRESET2                1110
#define IDC_BACKGROUNDRESET             1110
#define IDC_COLORLOOP                   1111
#define IDC_FONTBROWSE                  1112
#define IDC_COLORLOOP2                  1113
#define IDC_DISABLEUI                   1113
#define IDC_BACKGROUNDRESET2            1114
#define IDC_FONTRESET                   1114
#define ID_FILE_PLAYFILE                40001
#define ID_FILE_ADDFILE                 40002
#define ID_FILE_ADDFOLDER               40003
#define ID_FILE_LIBRARY                 40004
#define ID_PLAY_PLAY                    40005
#define ID_PLAY_SKIPFWD                 40006
#define ID_PLAY_SKIPBACK                40007
#define ID_PLAY_INCREASERATE            40008
#define ID_PLAY_DECREASERATE            40009
#define ID_PLAY_RESETRATE               40010
#define ID_OPTIONS                      40021
#define ID_OPTIONS_PREFERENCES          40023
#define ID_PLAY_VOLUMEDOWN              40040
#define ID_PLAY_VOLUMEUP                40043
#define ID_PLAY_MUTE                    40044
#define ID_PLAY_PLAYPAUSE               40045
#define ID_PLAY_PAUSE                   40056
#define ID_PLAY_STOP                    40057
#define IDC_TOPREBAR                    40060
#define IDC_TOPTOOLBAR                  40061
#define ID_PLAYUSINGDEFAULTSETTINGS     40066
#define ID_PLAYUSINGCUSTOMSETTINGS      40067
#define ID_ADDFILE                      40068
#define ID_ADDFOLDER                    40069
#define ID_FILE_CLOSEFILE               40070
#define ID_PLAY_ADDFILE                 40075
#define ID_PLAY_ADDFOLDER               40076
#define ID_VIEW_LIBRARYPANEL            40078
#define ID_VIEW_KEYBOARD                40079
#define ID_VIEW_FULLSCREEN              40080
#define ID_VIEW_PLAYBACK                40081
#define ID_VIEW_LIBRARY                 40082
#define ID_VIEW_FLIPSCREEN              40083
#define ID_VIEW_CONTROLS                40084
#define ID_GRAPHICS_STOP                40086
#define ID_VIEW_NOFULLSCREEN            40094
#define ID_VIEW_ALWAYSONTOP             40096
#define ID_VIEW_RESETDEVICE             40100
#define ID__INACTIVITYTIMER             40101
#define ID_INACTIVITYTIMER              40102
#define IDC_INACTIVITYTIMER             40103
#define ID__POSITIONCONTROL             40104
#define IDC_POSNCTRL                    40105
#define ID__NOTEON                      40106
#define ID_PIANO_NOTEON                 40107
#define ID_MIDI_NOTEON                  40108
#define ID_VIEW_MOVEANDZOOM             40109
#define ID__CANCELZOOMANDMOVE           40112
#define ID_CANCELZOOMANDMOVE            40113
#define ID_VIEW_CANCELMOVEANDZOOM       40114
#define ID_VIEW_RESETMOVEANDZOOM        40115
#define ID_FILE_PRACTICESONG            40119
#define ID__CHANGESTATE                 40120
#define ID_CHANGESTATE                  40121
#define ID_Menu                         40122
#define ID_Menu40123                    40123
#define ID_LIBRARY_LEARNSONG            40124
#define ID_PRACTICE_DEFAULT             40126
#define ID_PRACTICE_CUSTOM              40127
#define ID__SETLABEL                    40128
#define ID_LIBRARY_REFRESH              40130
#define ID_FILE_REFRESH                 40131
#define ID_PLAY_LEARNING                40134
#define ID_LEARNING_ADAPTIVE            40135
#define ID_LEARNING_WAITING             40136
#define ID__GAMEERROR                   40141
#define ID_GAMEERROR                    40142
#define ID__SETLOOPSTART                40143
#define ID__SETLOOPEND                  40144
#define ID__CLEARLOOP                   40145
#define ID_TPM_SETLOOPSTART             40146
#define ID_TBM_SETLOOPSTART             40147
#define ID_TBM_SETLOOPEND               40148
#define ID_TBM_CLEARLOOP                40149
#define ID__CLEARLOOP40150              40150
#define ID_HELP_ABOUT                   40152
#define ID_HELP_BUYONLINE               40154
#define ID_HELP_REGISTER                40155
#define ID_Menu40156                    40156
#define ID_FILE_PRACTICESONGWITHCUSTOMSETTINGS 40157
#define ID_FILE_PRACTICESONGCUSTOM      40158
#define ID_PLAY_FASTERNOTES             40159
#define ID_PLAY_SLOWERNOTE              40160
#define ID_PLAY_RESET                   40161
#define ID_PLAY_NRESET                  40162
#define ID_PLAY_NSLOWER                 40164
#define ID_PLAY_NFASTER                 40166
#define ID_GRAPHICS_FASTERNOTES         40169
#define ID_GRAPHICS_SLOWERNOTES         40170
#define ID_GRAPHICS_RESET               40171
#define ID_VIEW_NOLIBRARYHERE           40172
#define ID_VIEW_SETWINDOWSIZE           40173
#define ID__UPDATE                      40174
#define ID_UPDATE                       40175

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        161
#define _APS_NEXT_COMMAND_VALUE         40176
#define _APS_NEXT_CONTROL_VALUE         1110
#define _APS_NEXT_SYMED_VALUE           102
#endif
#endif
