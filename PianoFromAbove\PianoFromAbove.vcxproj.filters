﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Header Files\Game">
      <UniqueIdentifier>{4621ad7e-4302-43e5-9966-01ff44add568}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Window">
      <UniqueIdentifier>{0c79002f-13b6-491f-9e9f-49c41620cc26}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\TinyXML">
      <UniqueIdentifier>{28f621df-e589-41be-970e-ea341e164d5b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Game">
      <UniqueIdentifier>{f2900634-e42d-4bc1-a684-b46d4fc32d2c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\TinyXML">
      <UniqueIdentifier>{63c847cc-04fa-43f9-ab47-d46f04e480ae}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Window">
      <UniqueIdentifier>{a2c7b7ec-66fe-4103-af68-597bbb44e45f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\d3dx12">
      <UniqueIdentifier>{537710c4-3d24-4238-84de-47714d4fd5c5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\imgui">
      <UniqueIdentifier>{803c357f-bd3f-4a0a-b499-7277ccf24ddf}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\imgui">
      <UniqueIdentifier>{70af3f92-80c2-4a6b-9a3f-e78908df5865}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\freetype">
      <UniqueIdentifier>{c36c51d1-b4c2-4094-be38-7b4356c644d8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\lzma">
      <UniqueIdentifier>{bf67d806-6ffe-44c8-8135-b5fd2410d46d}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Config.h">
      <Filter>Header Files\Game</Filter>
    </ClInclude>
    <ClInclude Include="GameState.h">
      <Filter>Header Files\Game</Filter>
    </ClInclude>
    <ClInclude Include="MIDI.h">
      <Filter>Header Files\Game</Filter>
    </ClInclude>
    <ClInclude Include="Misc.h">
      <Filter>Header Files\Game</Filter>
    </ClInclude>
    <ClInclude Include="ConfigProcs.h">
      <Filter>Header Files\Window</Filter>
    </ClInclude>
    <ClInclude Include="Globals.h">
      <Filter>Header Files\Window</Filter>
    </ClInclude>
    <ClInclude Include="MainProcs.h">
      <Filter>Header Files\Window</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files\Window</Filter>
    </ClInclude>
    <ClInclude Include="Renderer.h">
      <Filter>Header Files\Game</Filter>
    </ClInclude>
    <ClInclude Include="tinyxml\tinystr.h">
      <Filter>Header Files\TinyXML</Filter>
    </ClInclude>
    <ClInclude Include="tinyxml\tinyxml.h">
      <Filter>Header Files\TinyXML</Filter>
    </ClInclude>
    <ClInclude Include="d3dx12\d3dx12.h">
      <Filter>Header Files\d3dx12</Filter>
    </ClInclude>
    <ClInclude Include="d3dx12\d3dx12_barriers.h">
      <Filter>Header Files\d3dx12</Filter>
    </ClInclude>
    <ClInclude Include="d3dx12\d3dx12_check_feature_support.h">
      <Filter>Header Files\d3dx12</Filter>
    </ClInclude>
    <ClInclude Include="d3dx12\d3dx12_core.h">
      <Filter>Header Files\d3dx12</Filter>
    </ClInclude>
    <ClInclude Include="d3dx12\d3dx12_default.h">
      <Filter>Header Files\d3dx12</Filter>
    </ClInclude>
    <ClInclude Include="d3dx12\d3dx12_pipeline_state_stream.h">
      <Filter>Header Files\d3dx12</Filter>
    </ClInclude>
    <ClInclude Include="d3dx12\d3dx12_render_pass.h">
      <Filter>Header Files\d3dx12</Filter>
    </ClInclude>
    <ClInclude Include="d3dx12\d3dx12_resource_helpers.h">
      <Filter>Header Files\d3dx12</Filter>
    </ClInclude>
    <ClInclude Include="d3dx12\d3dx12_root_signature.h">
      <Filter>Header Files\d3dx12</Filter>
    </ClInclude>
    <ClInclude Include="d3dx12\d3dx12_state_object.h">
      <Filter>Header Files\d3dx12</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imconfig.h">
      <Filter>Header Files\imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui.h">
      <Filter>Header Files\imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_rectpack.h">
      <Filter>Header Files\imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_textedit.h">
      <Filter>Header Files\imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_truetype.h">
      <Filter>Header Files\imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_internal.h">
      <Filter>Header Files\imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_freetype.h">
      <Filter>Header Files\imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_impl_dx12.h">
      <Filter>Header Files\imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_impl_win32.h">
      <Filter>Header Files\imgui</Filter>
    </ClInclude>
    <ClInclude Include="freetype\freetype.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="ft2build.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftadvanc.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftbbox.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftbdf.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftbitmap.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftbzip2.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftcache.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftchapters.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftcid.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftcolor.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftdriver.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\fterrdef.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\fterrors.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftfntfmt.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftgasp.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftglyph.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftgxval.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftgzip.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftimage.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftincrem.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftlcdfil.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftlist.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftlogging.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftlzw.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftmac.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftmm.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftmodapi.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftmoderr.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftotval.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftoutln.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftparams.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftpfr.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftrender.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftsizes.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftsnames.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftstroke.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftsynth.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftsystem.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\fttrigon.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\fttypes.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ftwinfnt.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\otsvg.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\t1tables.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\ttnameid.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\tttables.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="freetype\tttags.h">
      <Filter>Header Files\freetype</Filter>
    </ClInclude>
    <ClInclude Include="lzma.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\base.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\bcj.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\block.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\check.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\container.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\delta.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\filter.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\hardware.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\index.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\index_hash.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\lzma12.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\stream_flags.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\version.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
    <ClInclude Include="lzma\vli.h">
      <Filter>Header Files\lzma</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="PianoFromAbove.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="tinyxml\tinystr.cpp">
      <Filter>Source Files\TinyXML</Filter>
    </ClCompile>
    <ClCompile Include="tinyxml\tinyxml.cpp">
      <Filter>Source Files\TinyXML</Filter>
    </ClCompile>
    <ClCompile Include="tinyxml\tinyxmlerror.cpp">
      <Filter>Source Files\TinyXML</Filter>
    </ClCompile>
    <ClCompile Include="tinyxml\tinyxmlparser.cpp">
      <Filter>Source Files\TinyXML</Filter>
    </ClCompile>
    <ClCompile Include="Config.cpp">
      <Filter>Source Files\Game</Filter>
    </ClCompile>
    <ClCompile Include="GameState.cpp">
      <Filter>Source Files\Game</Filter>
    </ClCompile>
    <ClCompile Include="MIDI.cpp">
      <Filter>Source Files\Game</Filter>
    </ClCompile>
    <ClCompile Include="Renderer.cpp">
      <Filter>Source Files\Game</Filter>
    </ClCompile>
    <ClCompile Include="Misc.cpp">
      <Filter>Source Files\Game</Filter>
    </ClCompile>
    <ClCompile Include="PianoFromAbove.cpp">
      <Filter>Source Files\Window</Filter>
    </ClCompile>
    <ClCompile Include="ConfigProcs.cpp">
      <Filter>Source Files\Window</Filter>
    </ClCompile>
    <ClCompile Include="MainProcs.cpp">
      <Filter>Source Files\Window</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui.cpp">
      <Filter>Source Files\imgui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_draw.cpp">
      <Filter>Source Files\imgui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_tables.cpp">
      <Filter>Source Files\imgui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_widgets.cpp">
      <Filter>Source Files\imgui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_impl_dx12.cpp">
      <Filter>Source Files\imgui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_impl_win32.cpp">
      <Filter>Source Files\imgui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_freetype.cpp">
      <Filter>Source Files\imgui</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Images\mediaiconssmall.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="Images\PFA Icon.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="Images\Mirror Small.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="images\Welcome.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="Images\Godowsky - Study of Chopin Etude Op. 10, No. 1.mid">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="Images\Lock.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="common.hlsli">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <FxCompile Include="rect_vs.hlsl">
      <Filter>Resource Files</Filter>
    </FxCompile>
    <FxCompile Include="rect_ps.hlsl">
      <Filter>Resource Files</Filter>
    </FxCompile>
    <FxCompile Include="note_vs.hlsl">
      <Filter>Resource Files</Filter>
    </FxCompile>
    <FxCompile Include="note_ps.hlsl">
      <Filter>Resource Files</Filter>
    </FxCompile>
    <FxCompile Include="background_vs.hlsl">
      <Filter>Resource Files</Filter>
    </FxCompile>
    <FxCompile Include="background_ps.hlsl">
      <Filter>Resource Files</Filter>
    </FxCompile>
  </ItemGroup>
</Project>